import 'dart:async';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:swadesic/features/buyers/buyer_view_store/buyer_view_store_screen.dart';
import 'package:swadesic/features/common_buyer_seller_screen/create_store_options/create_store_options.dart';
import 'package:swadesic/features/seller/seller_onboarding/seller_onboarding_get_started/seller_onboarding_get_started_screen.dart';
import 'package:swadesic/model/api_response_message.dart';
import 'package:swadesic/model/buyer_search_response/store_list_response.dart';
import 'package:swadesic/model/store_info/store_info.dart';
import 'package:swadesic/model/user_details_response/user_details_response.dart';
import 'package:swadesic/features/data_model/preview_store_data_model/preview_store_data_model.dart';
import 'package:swadesic/services/seller_home_service/seller_home_service.dart';
import 'package:swadesic/services/user_detail_services/user_detail_services.dart';
import 'package:swadesic/util/app_constants.dart';
import 'package:swadesic/util/common_methods.dart';
import 'package:swadesic/features/buyers/messaging/new_messaging_home_screen.dart';

enum SellerAccountsState { Loading, Success, Failed, Empty }

class SellerAccountsBloc {
  // region Common Variable
  BuildContext context;
  ///User detail response
  late GetUserDetailsResponse getUserDetailsResponse;
  late UserDetailsServices userDetailsServices;

  ///Get Store List
  late SellerHomeService sellerHomeService;
  late StoreListResponse storeListResponse;
  final String userReference;


  //late List<StoreList> storeList;


  // endregion


  //region Controller
  final sellerAccountCtrl = StreamController<SellerAccountsState>.broadcast();

  //endregion

  // region | Constructor |
  SellerAccountsBloc(this.context, this.userReference);

  // endregion

  // region Init
  void init() {
    sellerHomeService = SellerHomeService();
    userDetailsServices = UserDetailsServices();
    //Get getLoggedInUserDetail
    getLoggedInUserDetail();
  }
  // endregion



  //region Get Logged in user detail
  getLoggedInUserDetail() async {
    try {
      // sellerAccountCtrl.sink.add(SellerAccountsState.Success);
      getUserDetailsResponse = await userDetailsServices.getLoggedInUserDetail(userReference: AppConstants.appData.userReference!);
      ///Get store list
      getStoreList();

    } on ApiErrorResponseMessage {
      sellerAccountCtrl.sink.add(SellerAccountsState.Failed);

      // CommonMethods.toastMessage(AppStrings.error, context);
      return;
    } catch (error) {
      sellerAccountCtrl.sink.add(SellerAccountsState.Failed);

      // CommonMethods.toastMessage(AppStrings.error, context);
      return;
    }
  }
  //endregion



  //region Preview Stores
  List<PreviewStoreData> previewStores = [];
  bool isLoadingPreviewStores = false;
  String? previewStoresError;

  // Get preview stores for the user
  Future<void> getPreviewStores() async {
    try {
      isLoadingPreviewStores = true;
      previewStoresError = null;
      sellerAccountCtrl.sink.add(SellerAccountsState.Loading);

      previewStores = await sellerHomeService.getPreviewStores();

      sellerAccountCtrl.sink.add(SellerAccountsState.Success);
    } catch (error) {
      previewStoresError = 'Failed to load preview stores';
      sellerAccountCtrl.sink.add(SellerAccountsState.Failed);
      rethrow;
    } finally {
      isLoadingPreviewStores = false;
    }
  }

  // Handle tap on preview store
  void onTapPreviewStore(PreviewStoreData store) async {
    // Show loading message
    CommonMethods.toastMessage("Opening ${store.previewStoreName}", context);

    if (AppConstants.appData.isStoreView == true) {
      // If in store view, switch to buyer view first
      await _clearCache();
      
      if (context.mounted) {
        CommonMethods.toastMessage("Switching to buyer view", context, toastShowTimer: 3);
        // Switch to buyer view
        CommonMethods.switchToBuyerStorePreview(context: context, storeReference: store.previewStoreReference);
      }
    } else {
      // If already in buyer view, directly navigate to store
      if (context.mounted) {
        _navigateToStoreScreen(store);
      }
    }
  }

  void _navigateToStoreScreen(PreviewStoreData store) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => BuyerViewStoreScreen(
          storeReference: store.previewStoreReference,
        ),
      ),
    );
  }
  
  //endregion

  //region Get StoreList
  Future<void> getStoreList() async {
    try {
      storeListResponse = await sellerHomeService.getSellerStore();
      // Also load preview stores when loading store list
      await getPreviewStores();
    } on ApiErrorResponseMessage {
      sellerAccountCtrl.sink.add(SellerAccountsState.Failed);
      return;
    } catch (error) {
      sellerAccountCtrl.sink.add(SellerAccountsState.Failed);
      return;
    }
  }
//endregion

  //region On tap user profile
  void onTapUserProfile() async {
    //If already a user view
    if(AppConstants.appData.isUserView!){
      return;
    }

    // Clear cache before switching
    await _clearCache();

    //Show message that switching to the another store
    context.mounted?CommonMethods.toastMessage("Switching to ${getUserDetailsResponse.userDetail!.userName!}", AppConstants.userStoreCommonBottomNavigationContext,toastShowTimer: 3):null;
    //Switch to buyer
    CommonMethods.switchToBuyer(context: context);
  }
  //endregion

//region On tap store
  void onTapStore(StoreInfo storeInfo) async {
    //Else if static user then open login screen
    if(CommonMethods().isStaticUser()){
      return CommonMethods().goToSignUpFlow();
    }
    //If already in selected store
    if(AppConstants.appData.isStoreView! && AppConstants.appData.storeReference == storeInfo.storeReference){
      return;
    }

    // Clear cache before switching
    await _clearCache();

    //Show message that switching to the another store
    context.mounted?CommonMethods.toastMessage("Switching to ${storeInfo.storehandle!}", AppConstants.userStoreCommonBottomNavigationContext,toastShowTimer: 3):null;
    //Switch to seller (this will automatically set the Profile tab)
    CommonMethods.switchToSeller(storeReference: storeInfo.storeReference!, storeId:storeInfo.storeid!, context: context);
  }
//endregion

  Future<void> _clearCache() async {
    await NewMessagingHomeScreen.clearMessageCache(context);
  }

// region Go to Seller Create Store
  void goToSellerOnBoarding(){
    ///Check access
    ///Todo un-comment
    // if(BuyerHomeBloc.userDetailsResponse.userDetail!.createStore != "1"){
    //   CommonMethods.toastMessage(AppStrings.noAccess, context);
    //   return;
    // }
    var screen =  SellerOnBoardingGetStartedScreen(isTestStore: true,);
    var route = MaterialPageRoute(builder: (context) => screen);
    Navigator.push(AppConstants.userStoreCommonBottomNavigationContext, route);
  }
//endregion


  //region On tap Create
  Future<void> onTapCreateStore(){
    //Else if static user then open login screen
    if(CommonMethods().isStaticUser()){
       CommonMethods().goToSignUpFlow();
    }
    return CommonMethods.appMinimumBottomSheets(
      context: context,
      screen:  CreateStoreOptions(allowCreateStore: getUserDetailsResponse.userDetail!.createStore == "1"?true:false,
      allowCreateTestStore: getUserDetailsResponse.userDetail!.createTestStore == "1" ? true : false,
      ),
    );
  }
//endregion


}
