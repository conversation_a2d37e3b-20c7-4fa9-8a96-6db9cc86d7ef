import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:swadesic/features/data_model/store_dashboard_data_model/store_dashboard_data_model.dart';
import 'package:swadesic/features/seller/seller_store/store_dashboard/complete_check_list/complete_check_list.dart';
import 'package:swadesic/features/seller/seller_store/store_dashboard/dashboard_card_slider/dashboard_card_slider.dart';
import 'package:swadesic/features/seller/seller_store/store_dashboard/dashboard_card_slider/cards/finish_setup_card.dart';
import 'package:swadesic/features/seller/seller_store/store_dashboard/dashboard_card_slider/cards/make_store_visible_card.dart';
import 'package:swadesic/features/seller/seller_store/store_dashboard/dashboard_card_slider/cards/verification_in_progress_card.dart';
import 'package:swadesic/features/seller/seller_store/store_dashboard/dashboard_card_slider/cards/get_orders_card.dart';
import 'package:swadesic/features/seller/seller_store/store_dashboard/dashboard_card_slider/cards/orders_status_card.dart';
import 'package:swadesic/features/seller/seller_store/store_dashboard/dashboard_card_slider/cards/unlock_discovery_card.dart';
import 'package:swadesic/features/seller/seller_store/store_dashboard/dashboard_card_slider/cards/make_most_swadesic_card.dart';
import 'package:swadesic/features/seller/seller_store/store_dashboard/dashboard_card_slider/cards/store_valuation_card.dart';
import 'package:swadesic/features/seller/seller_all_orders/seller_all_orders_screen.dart';
import 'package:swadesic/features/seller/seller_store/seller_trust_center/id_verification/id_verification_screen.dart';
import 'package:swadesic/model/store_dashboard_response/store_dashboard_response.dart';
import 'package:swadesic/services/store_dashboard_services/store_dashboard_service.dart';
import 'package:swadesic/util/app_constants.dart';
import 'package:swadesic/util/app_strings.dart';
import 'package:swadesic/util/common_methods.dart';

class DashboardCardSliderBloc {
  final BuildContext context;
  final String storeReference;
  final StoreDashboardService _dashboardService = StoreDashboardService();

  DashboardCardSliderBloc(this.context, this.storeReference);

  void dispose() {
    // Clean up any resources if needed
  }

  List<DashboardCard> getVisibleCards(StoreDashBoard storeDashboard) {
    final List<DashboardCard> cards = [];

    // 1st card: Finish Setup & Start Sharing
    if (_shouldShowFinishSetupCard(storeDashboard)) {
      cards.add(_buildFinishSetupCard(storeDashboard));
    }

    // 2nd card: Make Your Store Visible
    if (_shouldShowMakeStoreVisibleCard(storeDashboard)) {
      cards.add(_buildMakeStoreVisibleCard(storeDashboard));
    }

    // 3rd card: Verification in Progress
    if (_shouldShowVerificationInProgressCard(storeDashboard)) {
      cards.add(_buildVerificationInProgressCard(storeDashboard));
    }

    // 4th card: Get orders on Swadesic
    if (_shouldShowGetOrdersCard(storeDashboard)) {
      cards.add(_buildGetOrdersCard(storeDashboard));
    }

    // 5th card: Orders and their statuses
    if (_shouldShowOrdersStatusCard(storeDashboard)) {
      cards.add(_buildOrdersStatusCard(storeDashboard));
    }

    // 6th card: Unlock Public Discovery
    if (_shouldShowUnlockDiscoveryCard(storeDashboard)) {
      cards.add(_buildUnlockDiscoveryCard(storeDashboard));
    }

    // 7th card: Make the Most of Swadesic
    cards.add(_buildMakeMostSwadesicCard(storeDashboard));

    // 8th card: Store valuation
    cards.add(_buildStoreValuationCard(storeDashboard));

    return cards;
  }

  // Card criteria methods
  bool _shouldShowFinishSetupCard(StoreDashBoard storeDashboard) {
    // Shows up when basic onboarding and trust center completion is not done
    return storeDashboard.dashboardProgress < 100 ||
        !(storeDashboard.trustcenterDetail ?? false);
  }

  bool _shouldShowMakeStoreVisibleCard(StoreDashBoard storeDashboard) {
    // When onboarding is complete but store is currently not activated
    return storeDashboard.dashboardProgress >= 100 &&
        !(storeDashboard.isActive ?? false);
  }

  bool _shouldShowVerificationInProgressCard(StoreDashBoard storeDashboard) {
    // If verification request status is waiting
    return storeDashboard.storeVerificationRequestStatus == "PENDING";
  }

  bool _shouldShowGetOrdersCard(StoreDashBoard storeDashboard) {
    // If first_activated_date exists and verification is true and store is 'close for orders'
    return (storeDashboard.firstActivatedDate?.isNotEmpty ?? false) &&
        (storeDashboard.isVerificationCompleted ?? false) &&
        !(storeDashboard.openForOrder ?? false);
  }

  bool _shouldShowOrdersStatusCard(StoreDashBoard storeDashboard) {
    // If first activated date and first verified date is present
    return (storeDashboard.firstActivatedDate?.isNotEmpty ?? false) &&
        (storeDashboard.firstVerifiedDate?.isNotEmpty ?? false);
  }

  bool _shouldShowUnlockDiscoveryCard(StoreDashBoard storeDashboard) {
    // If first activated date and first verified date is present and public discovery is false
    return (storeDashboard.firstActivatedDate?.isNotEmpty ?? false) &&
        (storeDashboard.firstVerifiedDate?.isNotEmpty ?? false) &&
        !(storeDashboard.isDiscoverable ?? false);
  }

  // Card builder methods
  DashboardCard _buildFinishSetupCard(StoreDashBoard storeDashboard) {
    return DashboardCard(
      titleWidget: FinishSetupCard.buildTitle(),
      contentWidget: FinishSetupCard.buildContent(storeDashboard),
      onTap: () => _showCompleteCheckList(storeDashboard),
    );
  }

  DashboardCard _buildMakeStoreVisibleCard(StoreDashBoard storeDashboard) {
    return DashboardCard(
      titleWidget: MakeStoreVisibleCard.buildTitle(),
      contentWidget: MakeStoreVisibleCard.buildContent(),
      onTap: () => _activateStore(storeDashboard),
    );
  }

  DashboardCard _buildVerificationInProgressCard(
      StoreDashBoard storeDashboard) {
    return DashboardCard(
      titleWidget: VerificationInProgressCard.buildTitle(),
      contentWidget: VerificationInProgressCard.buildContent(),
      onTap: () => _goToTrustCenter(),
    );
  }

  DashboardCard _buildGetOrdersCard(StoreDashBoard storeDashboard) {
    return DashboardCard(
      titleWidget: GetOrdersCard.buildTitle(),
      contentWidget: GetOrdersCard(storeDashboard: storeDashboard),
      onTap: () => _toggleOpenForOrders(),
    );
  }

  DashboardCard _buildOrdersStatusCard(StoreDashBoard storeDashboard) {
    return DashboardCard(
      titleWidget: OrdersStatusCard.buildTitle(),
      contentWidget: OrdersStatusCard.buildContent(storeDashboard),
      onTap: () => _goToOrdersPage(),
    );
  }

  DashboardCard _buildUnlockDiscoveryCard(StoreDashBoard storeDashboard) {
    return DashboardCard(
      titleWidget: UnlockDiscoveryCard.buildTitle(),
      contentWidget: UnlockDiscoveryCard.buildContent(),
      onTap: () => _showPublicDiscoveryBottomSheet(),
    );
  }

  DashboardCard _buildMakeMostSwadesicCard(StoreDashBoard storeDashboard) {
    return DashboardCard(
      titleWidget: MakeMostSwadesicCard.buildTitle(),
      contentWidget: MakeMostSwadesicCard.buildContent(),
      onTap: () => _showMakeMostSwadesicBottomSheet(),
    );
  }

  DashboardCard _buildStoreValuationCard(StoreDashBoard storeDashboard) {
    return DashboardCard(
      titleWidget: StoreValuationCard.buildTitle(storeDashboard),
      contentWidget: StoreValuationCard.buildContent(storeDashboard, context),
      onTap: () => _showStoreValuationOverlay(),
    );
  }

  // Action methods
  void _showCompleteCheckList(StoreDashBoard storeDashboard) {
    CommonMethods.appMinimumBottomSheets(
      bottomSheetName: AppStrings.completeStoreActivation,
      screen: CompleteCheckList(
        storeDashBoard: storeDashboard,
        previousScreenContext: context,
        storeId: AppConstants.appData.storeId!,
      ),
      context: context,
    );
  }

  Future<void> _activateStore(StoreDashBoard storeDashboard) async {
    final storeDashboardDataModel =
        Provider.of<StoreDashboardDataModel>(context, listen: false);

    try {
      // Show confetti if first time activation
      if (storeDashboard.firstActivatedDate?.isEmpty ?? true) {
        CommonMethods.showCelebration(
          context: context,
          isPlay: true,
          celebrationMessage: "Your store is now visible",
        );
      }

      await _dashboardService.activeAndDeActiveStore(
        storeReference: storeReference,
        storeActiveStatus: true,
      );

      // Update local state
      storeDashboardDataModel.storeDashBoard.isActive = true;
      storeDashboardDataModel.updateUi();

      CommonMethods.toastMessage("Your store is now visible", context);
    } catch (e) {
      CommonMethods.toastMessage(AppStrings.commonErrorMessage, context);
    }
  }

  void _goToTrustCenter() {
    Navigator.push(
      context,
      MaterialPageRoute(builder: (context) => const IdVerificationScreen()),
    );
  }

  Future<void> _toggleOpenForOrders() async {
    final storeDashboardDataModel =
        Provider.of<StoreDashboardDataModel>(context, listen: false);

    try {
      await _dashboardService.openCloseStore(storeReference: storeReference);

      storeDashboardDataModel.storeDashBoard.openForOrder =
          !(storeDashboardDataModel.storeDashBoard.openForOrder ?? false);
      storeDashboardDataModel.updateUi();

      CommonMethods.toastMessage(
        storeDashboardDataModel.storeDashBoard.openForOrder!
            ? AppStrings.yourStoreIsCurrentlyOpen
            : AppStrings.yourStoreIsCurrentlyClosed,
        context,
      );
    } catch (e) {
      CommonMethods.toastMessage(AppStrings.commonErrorMessage, context);
    }
  }

  void _goToOrdersPage() {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => SellerAllOrdersScreen(
          storeId: AppConstants.appData.storeId!,
        ),
      ),
    );
  }

  void _showPublicDiscoveryBottomSheet() {
    // Implementation will be added in the card widget
    UnlockDiscoveryCard.showBottomSheet(
        context, storeReference, _dashboardService);
  }

  void _showMakeMostSwadesicBottomSheet() {
    // Implementation will be added in the card widget
    MakeMostSwadesicCard.showBottomSheet(context);
  }

  void _showStoreValuationOverlay() {
    // Implementation will be added in the card widget
    StoreValuationCard.showOverlay(context);
  }
}
