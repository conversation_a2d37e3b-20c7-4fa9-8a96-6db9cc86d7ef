import 'package:flutter/material.dart';
import 'package:swadesic/features/navigation/buyer_navigation.dart';
import 'package:swadesic/features/navigation/seller_navigation.dart';
import 'package:swadesic/util/app_constants.dart';

/// Navigation router that determines which navigation system to show
/// based on the user's current role (buyer vs seller)
class NavigationRouter extends StatefulWidget {
  final bool isFromOnboardingFlow;

  const NavigationRouter({
    super.key,
    this.isFromOnboardingFlow = false,
  });

  @override
  State<NavigationRouter> createState() => _NavigationRouterState();
}

class _NavigationRouterState extends State<NavigationRouter> {
  @override
  Widget build(BuildContext context) {
    // Determine which navigation to show based on user role
    final isUserView = AppConstants.appData.isUserView ?? true;
    final isStoreView = AppConstants.appData.isStoreView ?? false;

    // Show seller navigation if user is in store view mode
    if (isStoreView && !isUserView) {
      return const SellerNavigation();
    }
    
    // Default to buyer navigation
    return BuyerNavigation(
      isFromOnboardingFlow: widget.isFromOnboardingFlow,
    );
  }
}

/// Legacy compatibility wrapper for UserBottomNavigation
/// This allows existing code to continue working while using the new navigation system
class UserBottomNavigation extends StatelessWidget {
  final bool isFromOnboardingFlow;

  const UserBottomNavigation({
    super.key,
    this.isFromOnboardingFlow = false,
  });

  @override
  Widget build(BuildContext context) {
    return NavigationRouter(
      isFromOnboardingFlow: isFromOnboardingFlow,
    );
  }
}

/// Legacy compatibility wrapper for StoreBottomNavigation
/// This allows existing code to continue working while using the new navigation system
class StoreBottomNavigation extends StatelessWidget {
  const StoreBottomNavigation({super.key});

  @override
  Widget build(BuildContext context) {
    return const NavigationRouter();
  }
}
