import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:swadesic/features/widgets/mini_dashboard_tooltip_info/mini_dashboard_tooltip_info.dart';
import 'package:swadesic/model/store_dashboard_response/store_dashboard_response.dart';
import 'package:swadesic/util/app_colors.dart';
import 'package:swadesic/util/app_images.dart';
import 'package:swadesic/util/app_text_style.dart';
import 'package:swadesic/util/common_methods.dart';
import 'package:swadesic/features/widgets/app_animated_dialog/app_animated_dialog.dart';

class OrdersStatusCard {
  static Widget buildTitle() {
    return Text(
      "Orders and their statuses",
      style: AppTextStyle.access0(textColor: AppColors.appBlack),
    );
  }

  static Widget buildContent(StoreDashBoard storeDashboard) {
    return LayoutBuilder(
      builder: (context, constraints) {
        // Calculate available width for each item
        final itemWidth =
            (constraints.maxWidth - 40) / 5; // 40 for padding and spacing

        // First row - always visible
        return Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisAlignment: MainAxisAlignment.spaceAround,
          children: [
            _buildOrderStatusItem(AppImages.waitingForConfirmation,
                storeDashboard.waitingForConfirmation ?? 0, itemWidth),
            const SizedBox(width: 10),
            _buildOrderStatusItem(AppImages.confirmedNotYetShipped,
                storeDashboard.confirmedNotShipped ?? 0, itemWidth),
            const SizedBox(width: 10),
            _buildOrderStatusItem(AppImages.shippingInProgress,
                storeDashboard.shippingInProgress ?? 0, itemWidth),
            const SizedBox(width: 10),
            _buildOrderStatusItem(
                AppImages.delivered, storeDashboard.delivered ?? 0, itemWidth),
            const SizedBox(width: 10),
            // Dropdown arrow button
            Padding(
              padding: const EdgeInsets.all(10),
              child: GestureDetector(
                onTap: () =>
                    _showOrderStatusBottomSheet(context, storeDashboard),
                child: Icon(
                  Icons.keyboard_arrow_down,
                  color: AppColors.appBlack,
                  size: 20,
                ),
              ),
            ),
          ],
        );
      },
    );
  }

  static void _showOrderStatusBottomSheet(
      BuildContext context, StoreDashBoard storeDashboard) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(16)),
      ),
      builder: (context) => Container(
        padding: const EdgeInsets.all(20),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Drag handle
            Container(
              width: 40,
              height: 4,
              margin: const EdgeInsets.only(bottom: 16),
              decoration: BoxDecoration(
                color: Colors.grey[300],
                borderRadius: BorderRadius.circular(2),
              ),
            ),
            Text(
              'Order Statuses',
              style:
                  AppTextStyle.contentHeading0(textColor: AppColors.appBlack),
            ),
            const SizedBox(height: 50),
            // First row
            _buildStatusRow(storeDashboard, isFirstRow: true),
            const SizedBox(height: 40),
            // Second row
            _buildStatusRow(storeDashboard, isFirstRow: false),
            const SizedBox(height: 20),
            // Show status labels button
            Align(
              alignment: Alignment.centerRight,
              child: CupertinoButton(
                padding: EdgeInsets.zero,
                onPressed: () {
                  Navigator.pop(context); // Close the bottom sheet
                  showDialog(
                    context: context,
                    builder: (_) => AppAnimatedDialog(
                      child: MiniDashboardTooltipInfo(),
                    ),
                  );
                },
                child: Text(
                  "Show status labels",
                  style: AppTextStyle.smallText(
                      textColor: AppColors.writingBlack1),
                ),
              ),
            ),
            SizedBox(height: MediaQuery.of(context).viewInsets.bottom + 20),
          ],
        ),
      ),
    );
  }

  static Widget _buildStatusRow(StoreDashBoard storeDashboard,
      {required bool isFirstRow}) {
    return LayoutBuilder(
      builder: (context, constraints) {
        final itemWidth =
            (constraints.maxWidth - 40) / 5; // 40 for padding and spacing

        return Row(
          mainAxisAlignment: MainAxisAlignment.spaceAround,
          children: isFirstRow
              ? [
                  _buildOrderStatusItem(AppImages.waitingForConfirmation,
                      storeDashboard.waitingForConfirmation ?? 0, itemWidth),
                  _buildOrderStatusItem(AppImages.confirmedNotYetShipped,
                      storeDashboard.confirmedNotShipped ?? 0, itemWidth),
                  _buildOrderStatusItem(AppImages.shippingInProgress,
                      storeDashboard.shippingInProgress ?? 0, itemWidth),
                  _buildOrderStatusItem(AppImages.delivered,
                      storeDashboard.delivered ?? 0, itemWidth),
                ]
              : [
                  _buildOrderStatusItemVertical(AppImages.cancelled,
                      storeDashboard.cancelled ?? 0, itemWidth),
                  _buildOrderStatusItemVertical(AppImages.returnRequested,
                      storeDashboard.returnRequested ?? 0, itemWidth),
                  _buildOrderStatusItemVertical(AppImages.returnPickup,
                      storeDashboard.returnToPickup ?? 0, itemWidth),
                  _buildOrderStatusItemVertical(AppImages.returned,
                      storeDashboard.returned ?? 0, itemWidth),
                  _buildOrderStatusItemVertical(AppImages.refunded,
                      storeDashboard.refunded ?? 0, itemWidth),
                  _buildOrderStatusItemVertical(AppImages.refundHold,
                      storeDashboard.refundHold ?? 0, itemWidth),
                ],
        );
      },
    );
  }

  static Widget _buildOrderStatusItem(
      String iconPath, int count, double itemWidth) {
    return SizedBox(
      width: itemWidth,
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Image.asset(
            iconPath,
            width: 24,
            height: 24,
            fit: BoxFit.contain,
          ),
          const SizedBox(width: 4),
          Text(
            count.toString(),
            style: AppTextStyle.access1(textColor: AppColors.appBlack),
            overflow: TextOverflow.ellipsis,
            maxLines: 1,
          ),
        ],
      ),
    );
  }

  static Widget _buildOrderStatusItemVertical(
      String iconPath, int count, double itemWidth) {
    return SizedBox(
      width: itemWidth * .8, // Slightly wider to accommodate the text
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Image.asset(
            iconPath,
            width: 24,
            height: 24,
            fit: BoxFit.contain,
          ),
          const SizedBox(
              height: 4), // Changed from width to height for vertical spacing
          Text(
            count.toString(),
            style: AppTextStyle.access1(textColor: AppColors.appBlack),
            overflow: TextOverflow.ellipsis,
            maxLines: 1,
          ),
        ],
      ),
    );
  }

  static void showExpandedBottomSheet(
      BuildContext context, StoreDashBoard storeDashboard) {
    CommonMethods.appBottomSheet(
      bottomSheetName: "Order Statuses",
      screen: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              "Order Status Details",
              style: AppTextStyle.pageHeading(textColor: AppColors.appBlack),
            ),
            const SizedBox(height: 20),
            // Show all order statuses in expanded view
            buildContent(storeDashboard),
          ],
        ),
      ),
      context: context,
    );
  }
}
