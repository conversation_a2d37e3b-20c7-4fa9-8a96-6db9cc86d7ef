import 'package:flutter/material.dart';
import 'package:swadesic/features/data_model/notification_data_mdel/all_store_notification.dart';
import 'package:swadesic/features/data_model/notification_data_mdel/user_or_store_notification.dart';
import 'package:swadesic/features/navigation/core/base_navigation.dart';
import 'package:swadesic/features/navigation/widgets/custom_bottom_nav.dart';
import 'package:swadesic/features/navigation/config/navigation_config.dart';
import 'package:swadesic/features/navigation/config/navigation_actions.dart';
import 'package:swadesic/features/seller/seller_home/seller_home_screen.dart';
import 'package:swadesic/features/buyers/buyer_search/buyer_search_screen.dart';
import 'package:swadesic/features/seller/seller_all_orders/seller_all_orders_screen.dart';
import 'package:swadesic/features/notification/notification_screen.dart';
import 'package:swadesic/features/post/add_post/add_post_screen.dart';
import 'package:swadesic/features/buyers/buyer_view_store/buyer_view_store_screen.dart';
import 'package:swadesic/util/app_constants.dart';
import 'package:swadesic/util/auto_hide_navigation_service.dart';
import 'package:swadesic/features/data_model/seller_own_store_info_data_model/seller_own_store_info_data_model.dart';
import 'package:provider/provider.dart';
import 'package:swadesic/features/bottom_navigations/store_bottom_navigation/store_bottom_navigation_bloc.dart';
import 'package:swadesic/util/app_images.dart';

/// Seller navigation implementation with Home, Search, Orders, Notifications, Add + Profile tabs
class SellerNavigation extends BaseNavigation {
  const SellerNavigation({super.key});

  @override
  State<SellerNavigation> createState() => _SellerNavigationState();
}

class _SellerNavigationState extends BaseNavigationState<SellerNavigation> {
  // Tab configuration
  static const List<String> _tabIcons = [
    // Icons.home,
    AppImages.homeIconOutlined,
    AppImages.searchIconOutlined,
    AppImages.ordersIconOutlined,
    AppImages.notificationIconOutlined,
    AppImages.addIconOutlined,
  ];

  static const List<String> _tabIconsFilled = [
    AppImages.homeIconFilled,
    AppImages.searchIcon2Filled,
    AppImages.ordersIconFilled,
    AppImages.notificationIconFilled,
    AppImages.addIconOutlined,
  ];

  static const List<String> _tabNames = [
    "Home",
    "Search",
    "Orders",
    "Notifications",
    "Add",
  ];
  late final StoreBottomNavigationBloc storeBottomNavigationBloc;

  // Cache provider data to avoid repeated lookups
  SellerOwnStoreInfoDataModel? _cachedStoreInfoModel;
  UserOrStoreNotificationDataModel? _cachedUserNotificationModel;
  AllStoreNotificationDataModel? _cachedStoreNotificationModel;

  @override
  void initState() {
    super.initState();
    // Set up app constants for seller navigation
    AppConstants.userStoreCommonBottomNavigationContext = context;
    AppConstants.isBottomNavigationMounted.value = true;
    AppConstants.storePersistentTabController.index = 0;
    storeBottomNavigationBloc = StoreBottomNavigationBloc(context);
    storeBottomNavigationBloc.init();

    // Initialize navigation actions with context and navigator keys
    NavigationActions.initialize(context, navigatorKeys);

    // Cache provider data once to avoid repeated lookups
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (mounted) {
        _cacheProviderData();
      }
    });
  }

  void _cacheProviderData() {
    try {
      _cachedStoreInfoModel =
          Provider.of<SellerOwnStoreInfoDataModel>(context, listen: false);
      _cachedUserNotificationModel =
          Provider.of<UserOrStoreNotificationDataModel>(context, listen: false);
      _cachedStoreNotificationModel =
          Provider.of<AllStoreNotificationDataModel>(context, listen: false);
    } catch (e) {
      // Handle provider not found gracefully
      debugPrint('Provider data not available: $e');
    }
  }

  @override
  List<Widget> get tabPages {
    // Get tab screens from centralized configuration
    final configScreens = NavigationConfig.getTabScreens(NavigationType.seller);
    // Add profile screen
    configScreens.add(NavigationActions.buildSellerProfileScreen());
    return configScreens;
  }

  @override
  Widget buildCustomBottomNav() {
    // Use cached data or fallback to provider if cache is empty
    String? storeImageUrl;
    if (_cachedStoreInfoModel?.storeInfoResponse != null) {
      storeImageUrl = _cachedStoreInfoModel!.storeInfoResponse!.icon;
    } else {
      try {
        final storeInfo =
            Provider.of<SellerOwnStoreInfoDataModel>(context, listen: false)
                .storeInfoResponse;
        storeImageUrl = storeInfo?.icon;
      } catch (e) {
        storeImageUrl = null;
      }
    }

    final isProfileTab = currentIndex == tabCount - 1;
    return CustomBottomNav(
      currentIndex: isProfileTab
          ? 4
          : currentIndex, // Show last grouped tab as selected when on profile
      onTabSelected: (index) => _handleTabSelected(index),
      onDoubleTap: (index) => _handleDoubleTap(index),
      onHorizontalSwipe: onHorizontalSwipe,
      onLongPress: (index) => _handleLongPress(index),
      onIconSwipe: (index) => _handleIconSwipe(index),
      tabIcons: NavigationConfig.getTabIcons(NavigationType.seller),
      tabIconsFilled:
          NavigationConfig.getTabIcons(NavigationType.seller, filled: true),
      tabNames: NavigationConfig.getTabNames(NavigationType.seller),
      groupedTabCount: 5, // 5 tabs in the grouped section for seller
      profileWidget: ProfileAvatar(
          isSelected: isProfileTab, isStore: true, imageUrl: storeImageUrl),
    );
  }

  // Gesture handlers using centralized configuration
  void _handleTabSelected(int index) {
    if (index == 4) {
      // Add button tapped - pass the current tab's context
      storeBottomNavigationBloc.addPostAndProductBottomSheet(
        context: context,
        tabContext: navigatorKeys[currentIndex].currentContext,
      );
    } else if (index < 5) {
      NavigationConfig.executeGesture(
          NavigationType.seller, index, GestureType.tap);
      onTabSelected(index == 4 ? tabCount - 1 : index);
    } else {
      onTabSelected(index == 4 ? tabCount - 1 : index);
    }
  }

  void _handleDoubleTap(int index) {
    // if (index < 5) {
    //   NavigationConfig.executeGesture(NavigationType.seller, index, GestureType.doubleTap);
    // } else {
    //   // Profile tab double tap
    //   NavigationActions.doubleTapAction(NavigationActions.buildSellerProfileScreen(), 5);
    // }
    // resetTab(index == 4 ? tabCount - 1 : index);
  }

  void _handleLongPress(int index) {
    if (index < 5) {
      NavigationConfig.executeGesture(
          NavigationType.seller, index, GestureType.longPress);
    } else {
      // Profile tab long press
      NavigationActions.sellerProfileLongPress();
    }
  }

  void _handleIconSwipe(int index) {
    if (index < 5) {
      NavigationConfig.executeGesture(
          NavigationType.seller, index, GestureType.swipe);
    }
    onIconSwipe(index == 4 ? tabCount - 1 : index);
  }

  @override
  void onProfileTabSelected() {
    // After Profile switching behavior: Show profile tab showing Store page
    // This means when user taps profile, they stay on the profile tab
    // No special switching behavior needed for seller
  }

  @override
  void dispose() {
    AppConstants.isBottomNavigationMounted.value = false;
    super.dispose();
  }
}

/// Wrapper widget to handle auto-hide navigation for seller screens
class SellerNavigationWrapper extends StatefulWidget {
  final Widget child;
  final ScrollController? scrollController;

  const SellerNavigationWrapper({
    super.key,
    required this.child,
    this.scrollController,
  });

  @override
  State<SellerNavigationWrapper> createState() =>
      _SellerNavigationWrapperState();
}

class _SellerNavigationWrapperState extends State<SellerNavigationWrapper> {
  final AutoHideNavigationService _autoHideService =
      AutoHideNavigationService();

  @override
  void initState() {
    super.initState();
    _autoHideService.enableAutoHide();
    if (widget.scrollController != null) {
      _autoHideService.attachToScrollController(widget.scrollController!);
    }
  }

  @override
  void dispose() {
    _autoHideService.disableAutoHide();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return widget.child;
  }
}
