// ignore_for_file: unnecessary_null_comparison

import 'dart:async';
import 'dart:io';
import 'dart:typed_data';
import 'package:flutter/cupertino.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:image_cropper/image_cropper.dart';
import 'package:image_picker/image_picker.dart';
import 'package:rich_text_controller/rich_text_controller.dart';
import 'package:swadesic/features/common_buyer_seller_screen/search/search_screen.dart';
import 'package:swadesic/features/seller/add_image/add_image_screen.dart';
import 'package:swadesic/model/api_response_message.dart';
import 'package:swadesic/model/label_data/label_data.dart';
import 'package:swadesic/model/single_store_info_response/single_store_info_response.dart';
import 'package:swadesic/model/store_onboarding_response/add_business_category_response.dart';
import 'package:swadesic/model/store_onboarding_response/add_store_response.dart';
import 'package:swadesic/model/store_onboarding_response/add_web_link_response.dart';
import 'package:swadesic/model/store_onboarding_response/business_category_response.dart';
import 'package:swadesic/model/store_onboarding_response/business_type_response.dart';
import 'package:swadesic/model/store_onboarding_response/store_handle_check_response.dart';
import 'package:swadesic/services/seller_trust_center_service/seller_trust_center_service.dart';
import 'package:swadesic/services/store_info_service/store_info_service.dart';
import 'package:swadesic/services/store_onboarding_services/store_onboarding_add_store_services.dart';
import 'package:swadesic/services/store_onboarding_services/store_onboarding_add_web_link.services.dart';
import 'package:swadesic/services/store_onboarding_services/store_onboarding_business_category_services.dart';
import 'package:swadesic/services/platform_file_upload_service.dart';
import 'package:swadesic/services/upload_file_service.dart';
import 'package:swadesic/services/web_seller_onboarding_service.dart';
import 'package:swadesic/util/app_colors.dart';
import 'package:swadesic/util/app_constants.dart';
import 'package:swadesic/util/app_strings.dart';
import 'package:swadesic/util/common_methods.dart';
import 'package:swadesic/util/web_file_picker.dart';

enum StoreOnBoardingState { Loading, Success, Failed }

class SellerOnBoardingBloc {
  // region Common Variables
  BuildContext context;
  int currentPage = 0;
  List<Label> ownedList = [];

  late Label selectedLabel;
  final bool isTestAccount;

  late BusinessCategoryService businessCategoryService;
  late BusinessCategoryResponse businessCategoryResponse;
  late BusinessTypeResponse businessTypeResponse;
  late AddBusinessCategoryResponse addBusinessCategoryResponse;
  late String selectedBusinessCategory = "";
  late String selectedBusinessType = "";
  late AddStoreService addStoreService;
  late AddStoreResponse addStoreResponse;
  late AddWebLinkService addWebLinkService;
  late AddWebLinkResponse addWebLinkResponse;
  bool? isUrlValid;
  int currentTextFieldIndex = 0;
  // List<String> businessCategoryList = [];
  // List<String> searchedCategoryList = [];
  bool searchFieldVisibility = false;
  bool isBusinessDescVisible = false;
  bool? isStoreHandleAvailable;
  late String newBusinessCategory;
  int businessCategoryId = 0;
  String storeReference = "";
  List<TextEditingController> linkText = [TextEditingController()];
  late var uploadFileService = UploadFileService();

  ///Check Store Handle Available
  late SellerStoreHandleAvailableCheckResponse
      sellerStoreHandleAvailableCheckResponse;

  ///Url validation
  bool emailValid = false;

  ///Store service and model
  late SingleStoreInfoServices singleStoreInfoServices;
  late SingleStoreInfoResponse singleStoreInfoResponse =
      SingleStoreInfoResponse();

  ///Trust center service
  late SellerTrustCenterService sellerTrustCenterService =
      SellerTrustCenterService();

  ///Image pickup
  final ImagePicker picker = ImagePicker();
  final ImageCropper imageCrop = ImageCropper();
  late String fileName;
  late File files = File("");
  bool isImageSelected = false;

  // Web-specific properties
  Uint8List? webImageBytes;
  String? webImageName;
  String? webImageType;
  late WebSellerOnboardingService webSellerOnboardingService;
  late PlatformFileUploadService platformFileUploadService;

  // endregion

  //region Controller
  final storeOnBoardingCtrl =
      StreamController<StoreOnBoardingState>.broadcast();
  final onChangePageCtrl = StreamController<int>.broadcast();
  final addNewLinkCtrl = StreamController<bool>.broadcast();
  final categorySearchCtrl = StreamController<bool>.broadcast();
  // final onTapCategorySearchCtrl = StreamController<bool>.broadcast();
  final onTapBusinessDescCtrl = StreamController<bool>.broadcast();
  final logoCtrl = StreamController<bool>.broadcast();
  final onTextChangeCtrl = StreamController<bool>.broadcast();
  final onBoardingPageCtrl = PageController(initialPage: 0);
  final storeHandleCtrl = StreamController<bool>.broadcast();
  final labelCtrl = StreamController<bool>.broadcast();

  //endregion

  //region Text Editing Controller
  final TextEditingController storeNameTextCtrl = TextEditingController();
  // final TextEditingController storeCategoryTextCtrl = TextEditingController();
  // static final TextEditingController aboutBusinessTextctrl = TextEditingController();
  // final TextEditingController storeDescriptionTextCtrl = TextEditingController();
  final TextEditingController webLinkTextCtrl = TextEditingController();
  final TextEditingController storeHandleTextCtrl = TextEditingController();
  final FocusNode businessCategoryFocusNode = FocusNode();
  //endregion

  //region Color change Text editing controller
  late RichTextController aboutStoreTextCtrl;
  Map<RegExp, TextStyle> pattern = {
    RegExp(AppConstants.atTag): TextStyle(
        color: AppColors.brandBlack,
        fontFamily: AppConstants.rRegular,
        fontSize: 14,
        fontWeight: FontWeight.w400),
  };
  // TextEditingController commentTextFieldCtrl = TextEditingController();
  //endregion

  // region | Constructor |
  SellerOnBoardingBloc(this.context, this.isTestAccount);
  // endregion

  // region Init
  void init() {
    //Clear selected image
    AppConstants.selectedSingleImage = null;
    initializeStoreDescTextCtrl();

    ///Clear existing image
    AppConstants.multipleSelectedImage.clear();
    businessCategoryService = BusinessCategoryService();
    getBusinessCategoryApiCall();
    getBusinessTypeApiCall();
    addStoreService = AddStoreService();
    addWebLinkService = AddWebLinkService();
    singleStoreInfoServices = SingleStoreInfoServices();
    //Initialize web service
    webSellerOnboardingService = WebSellerOnboardingService();
    //Initialize platform file upload service
    platformFileUploadService = PlatformFileUploadService();
    businessCategoryFocusNode.addListener(() {
      onBusinessFocusChange();
    });
    addOwnedList();
  }
// endregion

  //region Add owned list
  void addOwnedList() {
    //Owned data
    ownedList.add(Label(
        "Swadeshi Owned (Swadeshi label of your store, Slide to select)",
        "Videshi Owned",
        0.0,
        "Businesses that are entirely foreign-owned",
        "NOT_SWADESHI_OWNED"));
    ownedList.add(Label(
        "Swadeshi Owned (Swadeshi label of your store, Slide to select)",
        "Partially Swadeshi Owned",
        1.0,
        "Businesses with a minority Indian ownership (below 50% equity) and majority foreign ownership",
        "PARTIALLY_SWADESHI_OWNED"));
    ownedList.add(Label(
        "Swadeshi Owned (Swadeshi label of your store, Slide to select)",
        "Mainly Swadeshi Owned",
        2.0,
        "Businesses with a majority Indian ownership (above 50% equity) but may have some foreign investment or involvement",
        "MAINLY_SWADESHI_OWNED"));
    ownedList.add(Label(
        "Swadeshi Owned (Swadeshi label of your store, Slide to select)",
        "Fully Swadeshi Owned",
        3.0,
        "Businesses that are fully Indian-owned and operated",
        "FULLY_SWADESHI_OWNED"));
    //Add default one if null
    selectedLabel = ownedList[1];
  }
  //endregion

  //region On business focus node
  void onBusinessFocusChange() {
    categorySearchCtrl.sink.add(true);

    //print("Busines category node ${businessCategoryFocusNode.hasFocus}");
  }
  //endregion

  //region Initialize Store Desc Text editing controller
  void initializeStoreDescTextCtrl() {
    aboutStoreTextCtrl = RichTextController(
        deleteOnBack: true,
        onMatch: (List<String> match) {
          pattern;
        },
        patternMatchMap: pattern);
  }

  //endregion

  //region On Tap Next
  void onTapNext() {
    currentPage++;
    if (currentPage >= 3) {
      currentPage = 0;
      return;
    }
    onBoardingPageCtrl.animateToPage(
      currentPage,
      duration: const Duration(milliseconds: 250),
      curve: Curves.easeInOut,
    );
    onChangePageCtrl.sink.add(currentPage);
  }
  //endregion

  //region On Change Page
  void onChangePage(int index) {
    currentPage = index;
    onChangePageCtrl.sink.add(index);
  }
  //endregion

  //region On Change text
  onTextChange() {
    //print('hello');
    onTextChangeCtrl.sink.add(true);
  }
  //endregion

  //region Get Business Category
  void getBusinessCategoryApiCall() async {
    try {
      //editProductCtrl.sink.add(HiddenProductState.Loading);
      businessCategoryResponse =
          await businessCategoryService.getBusinessCategory();
      //
      // for(int i = 0; i < businessCategoryResponse.businessCategoryList!.length;i++){
      //   businessCategoryList.add(businessCategoryResponse.businessCategoryList![i].categoryName.toString()) ;
      // }
      // editProductCtrl.sink.add(HiddenProductState.Success);
    } on ApiErrorResponseMessage catch (error) {
      //editProductCtrl.sink.add(HiddenProductState.Failed);
      CommonMethods.toastMessage(AppStrings.commonErrorMessage, context);
      return;
    } catch (error) {
      // editProductCtrl.sink.add(HiddenProductState.Failed);
      CommonMethods.toastMessage(AppStrings.commonErrorMessage, context);

      return;
    }
  }

//endregion

  //region Get Business type
  void getBusinessTypeApiCall() async {
    try {
      //editProductCtrl.sink.add(HiddenProductState.Loading);
      businessTypeResponse = await businessCategoryService.getBusinessType();
      //
      // for(int i = 0; i < businessCategoryResponse.businessCategoryList!.length;i++){
      //   businessCategoryList.add(businessCategoryResponse.businessCategoryList![i].categoryName.toString()) ;
      // }
      // editProductCtrl.sink.add(HiddenProductState.Success);
    } on ApiErrorResponseMessage {
      //editProductCtrl.sink.add(HiddenProductState.Failed);
      CommonMethods.toastMessage(AppStrings.commonErrorMessage, context);
      return;
    } catch (error) {
      CommonMethods.toastMessage(AppStrings.commonErrorMessage, context);

      return;
    }
  }

//endregion
  //region On tap business type
  void onTapBusinessType() {
    List<String> dataList = [];
    for (var data in businessTypeResponse.data!) {
      dataList.add(data.businessType!);
    }
    var screen = SearchScreen(
      dataList: dataList,
      searchTitle: AppStrings.selectBusinessType,
      isAddFeatureEnable: true,
    );

    var route = CupertinoPageRoute(builder: (context) => screen);
    Navigator.push(context, route).then((value) {
      if (value == null) {
        return;
      }
      selectedBusinessType = value;
      storeOnBoardingCtrl.sink.add(StoreOnBoardingState.Success);
    });
  }

//endregion

  //region Add Store
  void addStoreApiCall() async {
    try {
      ///Store handle is empty
      if (storeHandleTextCtrl.text.trim().isEmpty) {
        return CommonMethods.toastMessage(
            AppStrings.storeHandleCanNotBeEmpty, context);
      }

      ///Store handle check status is null. It means api crashed
      if (isStoreHandleAvailable == null) {
        return CommonMethods.toastMessage(
            AppStrings.unableToCheckStoreHandle, context);
      }

      ///Store handle is not available check
      if (isStoreHandleAvailable != null && isStoreHandleAvailable! == false) {
        return CommonMethods.toastMessage(
            AppStrings.storeHandleIsNotAvailable, context);
      }

      ///Check store name
      if (storeNameTextCtrl.text.replaceAll(" ", "").isEmpty) {
        return CommonMethods.toastMessage(
            AppStrings.storeNameCanNotBeEmpty, context);
      }

      ///Check Business category
      if (selectedBusinessCategory.isEmpty) {
        return CommonMethods.toastMessage(
            AppStrings.pleaseSelectBusinessCategory, context);
      }

      ///Check store logo
      if (kIsWeb) {
        // Web platform - check webImageBytes
        if (webImageBytes == null) {
          onBoardingPageCtrl.jumpToPage(0);
          return CommonMethods.toastMessage(
              AppStrings.pleaseAddTheStoreLogo, context);
        }
      } else {
        // Mobile platform - check files.path
        if (files.path.isEmpty) {
          onBoardingPageCtrl.jumpToPage(0);
          return CommonMethods.toastMessage(
              AppStrings.pleaseAddTheStoreLogo, context);
        }
      }

      ///Check Business type
      if (selectedBusinessType.isEmpty) {
        return CommonMethods.toastMessage(
            AppStrings.pleaseSelectBusinessType, context);
      }

      ///Check About your business
      if (aboutStoreTextCtrl.text.replaceAll(" ", "").isEmpty) {
        return CommonMethods.toastMessage(
            AppStrings.aboutYourBusinessCanNotEmpty, context);
      }

      ///Check email list empty or not and also check is valid
      ///Check URL validation
      if (isUrlValid != null) {
        ///Check is valid
        if (!isUrlValid!) {
          return CommonMethods.toastMessage(AppStrings.invalidUrl, context);
        }
      }
      storeOnBoardingCtrl.sink.add(StoreOnBoardingState.Loading);

      ///Check selected business category is null or not
      // if(selectedBusinessCategory.categoryid == null){
      //   await addBusinessCategoryApiCall();
      // }

      ///Add Store Api Call
      addStoreResponse = await addStoreService.addStore(
          storeName: storeNameTextCtrl.text,
          aboutStore: aboutStoreTextCtrl.text,
          createdBy: AppConstants.appData.userId.toString(),
          modifiedBy: AppConstants.appData.userId.toString(),
          // BuyerHomeBloc.userDetailsResponse.userDetail!.userLocation!,
          storeHandle: isTestAccount
              ? "test_${storeHandleTextCtrl.text}"
              : storeHandleTextCtrl.text,
          selectedBusinessCategory: selectedBusinessCategory,
          businessType: selectedBusinessType,
          isTestStore: isTestAccount);
      storeReference = addStoreResponse.data!.storeReference!;
      //print(storeReference);

      ///Add Website Link
      await addWebsiteLinksApiCall();

      ///Upload Store Logo
      await startUpload();

      ///Add owned label
      await updateOwned();

      ///Get store info
      await getSingleStoreInfo();

      // Store created successfully, now handle navigation
      if (context.mounted) {
        //Switch to seller
        CommonMethods.switchToSeller(
            storeReference: addStoreResponse.data!.storeReference!,
            storeId: addStoreResponse.data!.storeid!,
            context: context);

        //Show message that store has been created successfully
        CommonMethods.toastMessage(
            AppStrings.congratulationYourStoreHasBeenCreated, context,
            toastShowTimer: 5);
      }

      //Switch tab to Profile (index 5)
      await Future.delayed(const Duration(seconds: 1));
      AppConstants.storePersistentTabController.jumpToTab(5);
      //Success
      storeOnBoardingCtrl.sink.add(StoreOnBoardingState.Success);
    } on ApiErrorResponseMessage catch (error) {
      storeOnBoardingCtrl.sink.add(StoreOnBoardingState.Failed);
      if (context.mounted) {
        CommonMethods.toastMessage(error.message.toString(), context);
      }
      return;
    } catch (error) {
      //print(error);
      storeOnBoardingCtrl.sink.add(StoreOnBoardingState.Failed);
      if (context.mounted) {
        CommonMethods.toastMessage(AppStrings.commonErrorMessage, context);
      }
      return;
    }
  }
  //endregion

  //region Update swadeshi owned
  Future<void> updateOwned() async {
    try {
      //Update swadeshi owned
      await sellerTrustCenterService.updateSwadeshiOwned(
          label: selectedLabel.labelStatus,
          storeReference: addStoreResponse.data!.storeReference!);
    } on ApiErrorResponseMessage {
      //Failed
      if (context.mounted) {
        CommonMethods.toastMessage(AppStrings.commonErrorMessage, context);
      }
      return;
    } catch (error) {
      //Failed
      if (context.mounted) {
        CommonMethods.toastMessage(AppStrings.commonErrorMessage, context);
      }
      return;
    }
  }
  //endregion

  //region Get Store Info Api call
  getSingleStoreInfo() async {
    try {
      singleStoreInfoResponse =
          await singleStoreInfoServices.getSingleStoreInfo(storeReference);
      //Store id
      AppConstants.appData.storeId = singleStoreInfoResponse.data!.storeid!;
      //Store reference
      AppConstants.appData.storeReference =
          singleStoreInfoResponse.data!.storeReference!;
    } on ApiErrorResponseMessage {
      if (context.mounted) {
        CommonMethods.toastMessage(AppStrings.commonErrorMessage, context);
      }
      return;
    } catch (error) {
      if (context.mounted) {
        CommonMethods.toastMessage(AppStrings.commonErrorMessage, context);
      }
      return;
    }
  }
//endregion

  //region Upload Store Logo
  Future<void> startUpload() async {
    try {
      storeOnBoardingCtrl.sink.add(StoreOnBoardingState.Loading);

      // Get the URL for uploading the store logo
      String url =
          "${AppConstants.addStoreIcon}${addStoreResponse.data!.storeReference!}/";

      if (kIsWeb) {
        // Web platform
        if (webImageBytes == null ||
            webImageName == null ||
            webImageType == null) {
          // No image selected for web
          return;
        }

        // Upload the store logo using the web service
        await webSellerOnboardingService.uploadStoreLogo(
          url: url,
          fileName: webImageName!,
          bytes: webImageBytes!,
          fileType: webImageType!,
        );
      } else {
        // Mobile platform
        if (files.path.isEmpty) {
          // No image selected for mobile
          return;
        }

        String fileName = files.path.split("/").last;

        // Upload the store logo using the mobile service
        await uploadFileService.uploadStoreLogo(
          filePath: files.path,
          url: url,
          fileNameWithExtension: fileName,
          parameter: {},
        );
      }
    } on ApiErrorResponseMessage catch (error) {
      storeOnBoardingCtrl.sink.add(StoreOnBoardingState.Failed);
      if (context.mounted) {
        CommonMethods.toastMessage(error.message.toString(), context);
      }
      return;
    } catch (error) {
      storeOnBoardingCtrl.sink.add(StoreOnBoardingState.Failed);
      if (context.mounted) {
        CommonMethods.toastMessage(error.toString(), context);
      }
      return;
    }
  }
  //endregion

  //region Add Website Links
  Future<void> addWebsiteLinksApiCall() async {
    // If linkText is empty, return
    if (linkText.isEmpty) {
      //print("No links provided.");
      return;
    }

    try {
      for (var data in linkText) {
        // Check if the controller is empty
        if (data.text.isEmpty) {
          continue; // Skip this iteration and move to the next link
        }
        await addWebLinkService.addStoreLink(
          linkName: CommonMethods.urlToWebsiteName(
              url: CommonMethods.addHttpAndHttps(url: data.text)),
          storeLink: CommonMethods.addHttpAndHttps(url: data.text),
          storeReference: storeReference,
        );
      }

      //
      // for (int i = 0; i < linkText.length; i++) {
      //   // Check if the controller is empty
      //   if (linkText[i].text.isEmpty) {
      //     //print("Empty link at index $i.");
      //     continue; // Skip this iteration and move to the next link
      //   }
      //
      //   await addWebLinkService.addStoreLink(
      //     linkName: CommonMethods.urlToWebsiteName(url: linkText[i].text),
      //     storeLink: CommonMethods.addHttpAndHttps(url: linkText[i].text),
      //     storeReference: storeReference,
      //   );
      // }

      // BusinessCategoryId = addBusinessCategoryResponse.data!.categoryid!;
      // return;
    } on ApiErrorResponseMessage catch (error) {
      if (context.mounted) {
        CommonMethods.toastMessage(error.message.toString(), context);
      }
      return;
    } catch (error) {
      //print(error);
      return;
    }
  }

//endregion

  //region On Tap business category Text Field
  onTapCategorySearch() {
    searchFieldVisibility = !searchFieldVisibility;
    categorySearchCtrl.sink.add(true);
  }
  //endregion

  //region On Tap business Description
  onTapBusinessDescField() {
    isBusinessDescVisible = !isBusinessDescVisible;
    onTapBusinessDescCtrl.sink.add(isBusinessDescVisible);
  }
  //endregion

  //region Go to Add Image Screen
  void goToAddImageScreen() async {
    if (kIsWeb) {
      // Web platform - use web file picker
      try {
        final result = await WebFilePicker.pickImage();
        if (result != null) {
          // Store the image data for web
          webImageBytes = result['bytes'];
          webImageName = result['name'];
          webImageType = result['type'];

          // Set image selected flag
          isImageSelected = true;
          logoCtrl.sink.add(true);
        }
      } catch (e) {
        debugPrint('Error picking image: $e');
      }
    } else {
      // Mobile platform - use image picker and cropper
      try {
        final picker = ImagePicker();
        final pickedFile = await picker.pickImage(
          source: ImageSource.gallery,
          imageQuality: 70,
        );

        if (pickedFile != null) {
          // Crop the image
          final croppedFile = await ImageCropper().cropImage(
            sourcePath: pickedFile.path,
            aspectRatio: const CropAspectRatio(ratioX: 1.0, ratioY: 1.0),
            uiSettings: [
              AndroidUiSettings(
                toolbarTitle: 'Crop Image',
                toolbarColor: AppColors.appBlack,
                toolbarWidgetColor: AppColors.appWhite,
                initAspectRatio: CropAspectRatioPreset.original,
                lockAspectRatio: true,
                backgroundColor: AppColors.appBlack,
                activeControlsWidgetColor: AppColors.appWhite
              ),
              IOSUiSettings(
                title: 'Crop Image',
                aspectRatioLockEnabled: true,
                aspectRatioPickerButtonHidden: true,
                resetAspectRatioEnabled: false,
              ),
            ],
          );

          if (croppedFile != null) {
            // Set the cropped file
            files = File(croppedFile.path);
            isImageSelected = true;
            logoCtrl.sink.add(true);
          }
        }
      } catch (e) {
        debugPrint('Error picking/cropping image: $e');
        // Show error to user if needed
        if (context.mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Failed to pick or crop image'),
            ),
          );
        }
      }
    }
  }
//endregion

  //region On category text Change
  void onCategoryTextChange(String text) {
    // searchedCategoryList = businessCategoryList.where((element)=>element.toLowerCase().contains(text.toLowerCase())).toList();
    // onTextChangeCtrl.sink.add(true);
    // categorySearchCtrl.sink.add(true);
    // //print(searchedCategoryList.toString());
  }
  //endregion

  //region On Tap Add Link
  void increaseTextField() {
    //Make field validation to null
    isUrlValid = null;
    linkText.add(TextEditingController());
    addNewLinkCtrl.sink.add(true);
  }
  //endregion

  //region Remove Text Field
  void removeTextField(int index) {
    linkText.removeAt(index);
    addNewLinkCtrl.sink.add(true);
  }
//endregion

//region If image is not Selected
// void defaultImage()async{
//   Future<File> getImageFileFromAssets(String path) async {
//     final byteData = await rootBundle.load('assets/$path');
//
//
//   }
//
//   File f = await getImageFileFromAssets('images/myImage.jpg');}
//endregion

  //region Check StoreHandle Available
  // checkStoreHandle()async{
  //   try{
  //     //If empty then return
  //     if(storeHandleTextCtrl.text.isEmpty){
  //       isStoreHandleAvailable = 2;
  //       storeHandleCtrl.sink.add(true);
  //       return;
  //     }
  //     // makeInBharatCtrl.sink.add(LetsMakeInBharatState.Loading);
  //     sellerStoreHandleAvailableCheckResponse = await addStoreService.checkStoreHandle(storeHandleTextCtrl.text);
  //     //Show user name available or not
  //     if(sellerStoreHandleAvailableCheckResponse.available == "false"){
  //       isStoreHandleAvailable = 1;
  //       storeHandleCtrl.sink.add(true);
  //     }
  //     else{
  //       isStoreHandleAvailable = 0;
  //       storeHandleCtrl.sink.add(true);
  //
  //
  //     }
  //     // makeInBharatCtrl.sink.add(LetsMakeInBharatState.Success);
  //   }
  //   on ApiErrorResponseMessage {
  //     // makeInBharatCtrl.sink.add(LetsMakeInBharatState.Failed);
  //     CommonMethods.toastMessage(AppStrings.error, context);
  //     return;
  //   }
  //   catch(error){
  //     // makeInBharatCtrl.sink.add(LetsMakeInBharatState.Failed);
  //     CommonMethods.toastMessage(AppStrings.error, context);
  //     return;
  //   }
  //
  // }
//endregion

//region Open Gallery
  void openGallery() async {
    try {
      XFile? galleryImage = await picker.pickImage(
        source: ImageSource.gallery,
      );
      if (galleryImage == null) return;
      //print(galleryImage.toString());
      ///Crop image
      crop(file: File(galleryImage.path));
    } catch (e) {
      //print("Error is $e");
    }
  }

//endregion

  //region Image crop
  crop({required File file}) async {
    files = (await CommonMethods.imageCrop(
        file: File(file.path), cropStyle: CropStyle.rectangle))!;
    if (files == null) {
      isImageSelected = false;
      logoCtrl.sink.add(true);
      return;
    }
    isImageSelected = true;
    logoCtrl.sink.add(true);
    // refreshCtrl.sink.add(true);
  }
//endregion

  //region On tap business category
  void onTapBusinessCategory() {
    List<String> dataList = [];
    for (var data in businessCategoryResponse.businessCategoryList!) {
      dataList.add(data.categoryName!);
    }
    var screen = SearchScreen(
      dataList: dataList,
      searchTitle: AppStrings.selectedBusinessCategory,
      isAddFeatureEnable: true,
    );
    var route = CupertinoPageRoute(builder: (context) => screen);
    Navigator.push(context, route).then((value) {
      if (value == null) {
        return;
      }

      selectedBusinessCategory = value;
      onTextChangeCtrl.sink.add(true);
      storeOnBoardingCtrl.sink.add(StoreOnBoardingState.Success);
    });
  }

//endregion
//region Check url validation
  void checkUrlValidation(
      {required TextEditingController currentUrlCtrl, required fieldIndex}) {
    //Add current field index to currentTextFieldIndex
    currentTextFieldIndex = fieldIndex;
    //print(currentUrlCtrl.text);
    if (currentUrlCtrl.text.isEmpty) {
      isUrlValid = null;
    } else {
      isUrlValid = CommonMethods.urlValidationCheck(url: currentUrlCtrl.text);
    }
    addNewLinkCtrl.sink.add(true);
  }
//endregion

//region Dispose
  void dispose() {
    labelCtrl.close;
  }
//endregion
}
