import 'package:badges/badges.dart' as badges;
import 'package:badges/badges.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:persistent_bottom_nav_bar/persistent_tab_view.dart';
import 'package:provider/provider.dart';
import 'package:swadesic/features/data_model/notification_data_mdel/all_store_notification.dart';
import 'package:swadesic/features/seller/seller_home/seller_home_screen.dart';
import 'package:swadesic/model/bottom_navigation_tab_context_model/bottom_tab_context.dart';
import 'package:swadesic/features/bottom_navigations/store_bottom_navigation/store_bottom_navigation_bloc.dart';
import 'package:swadesic/features/buyers/buyer_search/buyer_search_screen.dart';
import 'package:swadesic/features/buyers/buyer_search/initial_search_view_screen/initial_search_view_screen.dart';
import 'package:swadesic/features/buyers/buyer_view_store/buyer_view_store_screen.dart';
import 'package:swadesic/features/data_model/notification_data_mdel/user_or_store_notification.dart';
import 'package:swadesic/features/data_model/seller_own_store_info_data_model/seller_own_store_info_data_model.dart';
import 'package:swadesic/features/notification/notification_screen.dart';
import 'package:swadesic/features/seller/seller_all_orders/seller_all_orders_screen.dart';
import 'package:swadesic/features/seller/seller_invite_friend_and_customer/seller_invite_friend_and_customer_screen.dart';
import 'package:swadesic/model/buyer_search_response/store_list_response.dart';
import 'package:swadesic/model/store_info/store_info.dart';
import 'package:swadesic/util/app_colors.dart';
import 'package:swadesic/util/app_constants.dart';
import 'package:swadesic/util/app_images.dart';
import 'package:swadesic/util/common_methods.dart';
import 'package:swadesic/util/common_widgets.dart';

class StoreBottomNavigation extends StatefulWidget {
  const StoreBottomNavigation({
    Key? key,
  }) : super(key: key);

  @override
  State<StoreBottomNavigation> createState() => _StoreBottomNavigationState();
}

class _StoreBottomNavigationState extends State<StoreBottomNavigation> {
  //region Bloc
  late StoreBottomNavigationBloc storeBottomNavigationBloc;

  //endregion

  //Tab context
  late BottomTabContext bottomTabContext = BottomTabContext();

  //region Controller

  // final PersistentTabController _controller = PersistentTabController(initialIndex: 0);

  @override
  void initState() {
    storeBottomNavigationBloc = StoreBottomNavigationBloc(context);
    storeBottomNavigationBloc.init();
    //Add context to the global level
    AppConstants.userStoreCommonBottomNavigationContext = context;
    AppConstants.isBottomNavigationMounted.value = true;
    //print("Seller bottom navigation init is called");
    //Make initial tab to index 4
    AppConstants.storePersistentTabController.index = 4;
    super.initState();
  }

  @override
  void dispose() {
    super.dispose();
  }

  //Is bottom navigation visible
  bool isBottomNavigationVisible = true;

  @override
  Widget build(BuildContext context) {
    //region KeyBord visibility Check
    // AppConstants.isKeyboardVisible = MediaQuery.of(context).viewInsets.bottom != 0;

    //endregion
    // //print("Keybord is $AppConstants.isKeyboardVisible");
    return LayoutBuilder(
      builder: (context, boxConstraints) {
        if (boxConstraints.maxWidth < 600) {
          isBottomNavigationVisible = false;
          AppConstants.bottomNavigationRefreshCtrl.sink.add(true);
        } else {
          isBottomNavigationVisible = true;
          AppConstants.bottomNavigationRefreshCtrl.sink.add(true);
        }
        return Scaffold(
            body: PersistentTabView(
          context,
          controller: AppConstants.storePersistentTabController,
          decoration: NavBarDecoration(
            adjustScreenBottomPaddingOnCurve: false,
            boxShadow: AppColors.appShadow,
            colorBehindNavBar: AppColors.appWhite,
            borderRadius: const BorderRadius.only(
              topLeft: Radius.circular(10),
              topRight: Radius.circular(10),
            ),
          ),

          ///On select get context
          selectedTabScreenContext: (context) {
            //print("Tab index ${AppConstants.storePersistentTabController.index}");
            bottomTabContext.addContextAndIndex(
                context: context!,
                index: AppConstants.storePersistentTabController.index);
            // AppConstants.currentSelectedTab = context!;
            //print("tab is selected");
          },
          screens: [
            SellerHomeScreen(),
            // SellerInviteFriendAndCustomerScreen(userReference: AppConstants.appData.storeReference!),
            // const InitialSearchViewScreen(),
            const BuyerSearchScreen(),
            const SizedBox(),
            // SellerAllOrdersScreen(
            //   storeId: AppConstants.appData.storeId!,
            //   isFromBottomNavigation: true,
            // ),
            NotificationScreen(storeId: AppConstants.appData.storeId!),
            BuyerViewStoreScreen(
              isLeadingVisible: false,
              isStoreOwnerView: true,
              isFromBottomNavigation: true,
              storeReference: AppConstants.appData.storeReference!,
            ),
          ],
          confineInSafeArea: true,
          backgroundColor: AppColors.appWhite,
          // Default is Colors.white.
          handleAndroidBackButtonPress: true,
          // Default is true.
          resizeToAvoidBottomInset: true,
          // This needs to be true if you want to move up the screen when keyboard appears. Default is true.
          hideNavigationBarWhenKeyboardShows: true,

          ///

          ///
          popActionScreens: PopActionScreensType.all,

          ///
          // itemAnimationProperties: const ItemAnimationProperties( // Navigation Bar's items animation properties.
          //   duration: Duration(milliseconds: 00),
          //   curve: Curves.ease,
          // ),
          ///
          // screenTransitionAnimation: const ScreenTransitionAnimation( // Screen transition animation on change of selected tab.
          //   animateTabTransition: true,
          //   curve: Curves.ease,
          //   duration: Duration(milliseconds: 200),
          // ),
          popAllScreensOnTapOfSelectedTab: true,
          stateManagement: true,
          hideNavigationBar: isBottomNavigationVisible,

          // stateManagement: true,
          // onWillPop:(context)async{
          //   //print("yes");
          //   return true;
          // },

          items: _navBarsItems(),
          navBarStyle: NavBarStyle.style2,
        ));
        return StreamBuilder<bool>(
            stream: AppConstants.bottomNavigationRefreshCtrl.stream,
            builder: (context, snapshot) {
              return Scaffold(
                  body: PersistentTabView(
                context,
                controller: AppConstants.storePersistentTabController,
                decoration: NavBarDecoration(
                  adjustScreenBottomPaddingOnCurve: false,
                  boxShadow: AppColors.appShadow,
                  colorBehindNavBar: AppColors.appWhite,
                  borderRadius: const BorderRadius.only(
                    topLeft: Radius.circular(10),
                    topRight: Radius.circular(10),
                  ),
                ),

                ///On select get context
                selectedTabScreenContext: (context) {
                  //print("Tab index ${AppConstants.storePersistentTabController.index}");
                  bottomTabContext.addContextAndIndex(
                      context: context!,
                      index: AppConstants.storePersistentTabController.index);
                  // AppConstants.currentSelectedTab = context!;
                  //print("tab is selected");
                },
                screens: [
                  SellerHomeScreen(),
                  // SellerInviteFriendAndCustomerScreen(userReference: AppConstants.appData.storeReference!),
                  // const InitialSearchViewScreen(),
                  const BuyerSearchScreen(),
                  const SizedBox(),
                  // SellerAllOrdersScreen(
                  //   storeId: AppConstants.appData.storeId!,
                  //   isFromBottomNavigation: true,
                  // ),
                  NotificationScreen(storeId: AppConstants.appData.storeId!),
                  BuyerViewStoreScreen(
                    isStoreOwnerView: true,
                    isFromBottomNavigation: true,
                    storeReference: AppConstants.appData.storeReference!,
                  ),
                ],
                confineInSafeArea: true,
                backgroundColor: Colors.white,
                // Default is Colors.white.
                handleAndroidBackButtonPress: true,
                // Default is true.
                resizeToAvoidBottomInset: true,
                // This needs to be true if you want to move up the screen when keyboard appears. Default is true.
                hideNavigationBarWhenKeyboardShows: true,

                ///

                ///
                popActionScreens: PopActionScreensType.all,

                ///
                // itemAnimationProperties: const ItemAnimationProperties( // Navigation Bar's items animation properties.
                //   duration: Duration(milliseconds: 00),
                //   curve: Curves.ease,
                // ),
                ///
                // screenTransitionAnimation: const ScreenTransitionAnimation( // Screen transition animation on change of selected tab.
                //   animateTabTransition: true,
                //   curve: Curves.ease,
                //   duration: Duration(milliseconds: 200),
                // ),
                popAllScreensOnTapOfSelectedTab: true,
                stateManagement: true,
                hideNavigationBar: isBottomNavigationVisible,

                // stateManagement: true,
                // onWillPop:(context)async{
                //   //print("yes");
                //   return true;
                // },

                items: _navBarsItems(),
                navBarStyle: NavBarStyle.style2,
              ));
            });
      },
    );
  }

  List<PersistentBottomNavBarItem> _navBarsItems() {
    // Retrieve the data from the StoreInfoModel
    SellerOwnStoreInfoDataModel sellerOwnStoreInfoDataModel =
        Provider.of<SellerOwnStoreInfoDataModel>(context);
    StoreInfo? storeInfo = sellerOwnStoreInfoDataModel.storeInfo;
    // Retrieve the data from the User notification
    UserOrStoreNotificationDataModel userOrStoreNotificationDataModel =
        Provider.of<UserOrStoreNotificationDataModel>(context);
    // Retrieve the data from the All store notification
    AllStoreNotificationDataModel allStoreNotificationDataModel =
        Provider.of<AllStoreNotificationDataModel>(context);

    return [
      PersistentBottomNavBarItem(
        // onPressed: (data){
        //   AppConstants.persistentTabController.jumpToTab(0);
        //   buyerHomeScreen.refreshTimeSheetList();
        // },

        icon: SvgPicture.asset(
          AppImages.homeActive,
          height: 28,
          width: 28,
          color: AppColors.appBlack,
        ),
        inactiveIcon: SvgPicture.asset(
          AppImages.homeInActive,
          height: 28,
          width: 28,
          color: AppColors.appBlack,
        ),
        // onSelectedTabPressWhenNoScreensPushed: (){
        //   ShoppingCartBloc(context).init();
        //   //print("screen");
        // },

        //title: ("Home"),
        //activeColorPrimary: mainColor,
        //inactiveColorPrimary: CupertinoColors.systemGrey,
      ),
      PersistentBottomNavBarItem(
        icon: SvgPicture.asset(
          AppImages.searchActive,
          height: 28,
          width: 28,
          color: AppColors.appBlack,
        ),
        inactiveIcon: SvgPicture.asset(
          AppImages.searchInActive,
          height: 28,
          width: 28,
          color: AppColors.appBlack,
        ),
        // onPressed: (data) {
        ///Access check
        // if (BuyerHomeBloc.userDetailsResponse.data!.searchAndView! != "1") {
        //   return CommonMethods.snackBar(AppStrings.noAccess, context);
        // }
        //   AppConstants.persistentTabController.jumpToTab(1);
        // }
        //title: ("Search"),
        //activeColorPrimary: mainColor,
        //inactiveColorPrimary: CupertinoColors.systemGrey,
      ),

      ///Add post
      PersistentBottomNavBarItem(
        onPressed: (context) {
          storeBottomNavigationBloc.addPostAndProductBottomSheet(
              context: context!);
        },
        icon: SvgPicture.asset(
          AppImages.addPost,
          color: AppColors.appBlack,
          height: 28,
          width: 28,
        ),
        inactiveIcon: SvgPicture.asset(
          AppImages.addPost,
          height: 28,
          width: 28,
          color: AppColors.appBlack,
        ),
      ),
      // PersistentBottomNavBarItem(
      //     // onPressed: (data){
      //     //   AppConstants.persistentTabController.jumpToTab(2);
      //     //   shoppingCartScreen.refreshTimeSheetList();
      //     // },
      //     // onPressed: (data){
      //     //
      //     // },
      //     onSelectedTabPressWhenNoScreensPushed: () {
      //       //print("no screen pushed");
      //     },
      //     icon: SvgPicture.asset(AppImages.ordersActive),
      //     inactiveIcon: SvgPicture.asset(AppImages.ordersInActive)
      //
      //     // icon: AppConstants.cartItemIdList.isEmpty
      //     //     ? SvgPicture.asset(AppImages.basket)
      //     //     : Align(
      //     //         child: Badge(
      //     //           badgeColor: AppColors.brandGreen,
      //     //           padding: const EdgeInsets.all(5),
      //     //           badgeContent: Text(
      //     //             AppConstants.cartItemIdList.length.toString(),
      //     //             style: const TextStyle(color: AppColors.white, fontSize: 10, fontWeight: FontWeight.w700),
      //     //           ),
      //     //           //alignment: Alignment.center,
      //     //           position: BadgePosition.topEnd(),
      //     //           child: SvgPicture.asset(
      //     //             AppImages.basket,
      //     //             fit: BoxFit.contain,
      //     //             color: AppColors.appBlack,
      //     //           ),
      //     //         ),
      //     //       ),
      //
      //     // Stack(
      //     //   children: [
      //     //
      //     //     SvgPicture.asset(AppImages.basket),
      //     //     SvgPicture.asset(AppImages.basketActive),
      //     //
      //     //
      //     //     // Text("${AppConstants.cartItemIdList.length}",style: TextStyle(
      //     //     //   fontSize: 20,
      //     //     //   color: Colors.green
      //     //     // ),),
      //     //   ],
      //     // ),
      //     //title: ("Chat"),
      //     //activeColorPrimary: mainColor,
      //     //inactiveColorPrimary: CupertinoColors.systemGrey,
      //     ),

      ///Notification
      ///Notification
      PersistentBottomNavBarItem(
        inactiveIcon: Align(
          child: badges.Badge(
            badgeStyle: BadgeStyle(
              padding: EdgeInsets.all(5),
              badgeColor: AppColors.red,
            ),
            showBadge: (userOrStoreNotificationDataModel
                            .userOrStoreNotificationResponse!.notSeenCount! +
                        allStoreNotificationDataModel
                            .allStoreNotificationResponse!.notSeenCount!) ==
                    0
                ? false
                : true,
            badgeContent: Text(
              // CommonMethods.calculateNotificationCount(number: notificationDataModel.getNotificationResponse!.notSeenCount!!),
              "${(userOrStoreNotificationDataModel.userOrStoreNotificationResponse!.notSeenCount! + allStoreNotificationDataModel.allStoreNotificationResponse!.notSeenCount!) == 0 ? "" : (userOrStoreNotificationDataModel.userOrStoreNotificationResponse!.notSeenCount! + allStoreNotificationDataModel.allStoreNotificationResponse!.notSeenCount!) > 9 ? "+9" : (userOrStoreNotificationDataModel.userOrStoreNotificationResponse!.notSeenCount! + allStoreNotificationDataModel.allStoreNotificationResponse!.notSeenCount!)}",
              style: TextStyle(
                  color: AppColors.appWhite,
                  fontSize: 10,
                  fontWeight: FontWeight.w700),
            ),
//alignment: Alignment.center,
            position: BadgePosition.topEnd(),
            child: SvgPicture.asset(
              AppImages.notificationInActive,
              fit: BoxFit.contain,
              color: AppColors.appBlack,
              height: 28,
              width: 28,
            ),
          ),
        ),
        icon: Align(
          child: badges.Badge(
            showBadge: (userOrStoreNotificationDataModel
                            .userOrStoreNotificationResponse!.notSeenCount! +
                        allStoreNotificationDataModel
                            .allStoreNotificationResponse!.notSeenCount!) ==
                    0
                ? false
                : true,
            badgeStyle: BadgeStyle(
              padding: EdgeInsets.all(5),
              badgeColor: AppColors.red,
            ),
            badgeContent: Text(
              // CommonMethods.calculateNotificationCount(number:notificationDataModel.getNotificationResponse!.notSeenCount!!),
              "${(userOrStoreNotificationDataModel.userOrStoreNotificationResponse!.notSeenCount! + allStoreNotificationDataModel.allStoreNotificationResponse!.notSeenCount!) == 0 ? "" : (userOrStoreNotificationDataModel.userOrStoreNotificationResponse!.notSeenCount! + allStoreNotificationDataModel.allStoreNotificationResponse!.notSeenCount!) > 9 ? "+9" : (userOrStoreNotificationDataModel.userOrStoreNotificationResponse!.notSeenCount! + allStoreNotificationDataModel.allStoreNotificationResponse!.notSeenCount!)}",
              style: TextStyle(
                  color: AppColors.appWhite,
                  fontSize: 10,
                  fontWeight: FontWeight.w700),
            ),
            //alignment: Alignment.center,
            position: BadgePosition.topEnd(),
            child: SvgPicture.asset(
              AppImages.notificationActive,
              fit: BoxFit.contain,
              color: AppColors.appBlack,
              height: 28,
              width: 28,
            ),
          ),
        ),
      ),

      //Store
      PersistentBottomNavBarItem(
          onSelectedTabPressWhenNoScreensPushed: () {
            //print("hey");
          },

          // iconSize: 30,
          icon: CircleAvatar(
            radius: 15,
            backgroundColor: AppColors.brandBlack,
            child: Stack(
              alignment: Alignment.center,
              children: [
                Image.asset(
                  AppImages.indiaBoarder,
                  height: 30,
                  width: 30,
                ),
                Positioned.fill(
                  right: 0,
                  left: 0,
                  top: 0,
                  bottom: 0,
                  child: ClipOval(
                    child: storeInfo!.icon == null
                        ? Container(
                            padding: const EdgeInsets.all(2),
                            height: 28,
                            width: 28,
                            child: ClipRRect(
                              borderRadius: BorderRadius.circular(100),
                              child: SvgPicture.asset(
                                AppImages.storePlaceHolder,
                                height: 28,
                                width: 28,
                                fit: BoxFit.fill,
                              ),
                            ),
                          )
                        : Container(
                            padding: const EdgeInsets.all(2),
                            height: 28,
                            width: 28,
                            child: ClipRRect(
                              borderRadius: BorderRadius.circular(100),
                              child: extendedImage(
                                  storeInfo.icon, context, 50, 50,
                                  fit: BoxFit.fill,
                                  imageWidth: 28,
                                  imageHeight: 28,
                                  boxShape: BoxShape.circle,
                                  customPlaceHolder: AppImages.storePlaceHolder,
                                  borderColor: AppColors.brandBlack),
                            ),
                          ),
                  ),
                ),
              ],
            ),
          ),
          inactiveIcon: CircleAvatar(
            radius: 15,
            backgroundColor: AppColors.appWhite,
            child: ClipOval(
              child: storeInfo.icon == null
                  ? SvgPicture.asset(
                      AppImages.storePlaceHolder,
                      height: 28,
                      width: 28,
                      fit: BoxFit.fill,
                    )
                  : SizedBox(
                      // padding: const EdgeInsets.all(1),
                      height: 28,
                      width: 28,
                      child: ClipRRect(
                        borderRadius: BorderRadius.circular(15),
                        child: extendedImage(
                          storeInfo.icon, context, 50, 50, fit: BoxFit.fill,
                          imageWidth: 28,
                          imageHeight: 28,
                          boxShape: BoxShape.circle,
                          customPlaceHolder: AppImages.storePlaceHolder,
                          // borderColor:AppColors.brandGreen
                        ),
                      ),
                    ),
            ),
          )),
    ];
  }
}
