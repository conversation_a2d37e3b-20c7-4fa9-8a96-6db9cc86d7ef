import 'package:flutter/material.dart';
import 'package:swadesic/features/buyers/buyer_view_all_product/buyer_view_product/buyer_product_comment/buyer_product_comment_screen.dart';
import 'package:swadesic/util/app_constants.dart';

class CommentLink{

  Uri url ;
  String storeReference  = "";
  String productReference  = "";
  int commentId  = 0;
  bool isCommentLinkCalled = false;

  CommentLink(this.url){
    isCommentLinkCalled = true;
    takeOutValueFromUrl();
    action();
    //print(url.toString());
  }
  // String url = "co/?s=$storeReference&p=$productReference&cId=$commentId";

  //region Take out the value from URL
  void takeOutValueFromUrl(){
    productReference = url.queryParameters['p']!;
    storeReference = url.queryParameters['s']!;
    commentId = int.parse(url.queryParameters['cId']!);
  }
  //endregion


  //region Action
void action(){
  //Switch bottom navigation to index 0 for buyer and 5 for seller (Profile)
  AppConstants.userPersistentTabController.jumpToTab(0);
  AppConstants.storePersistentTabController.jumpToTab(5);
  var screen = BuyerProductCommentScreen(
    storeReference: storeReference,
    productRef: productReference,
    isWriteComment: false,
    selectedCommentId:commentId,
  );
  var route = MaterialPageRoute(builder: (context) => screen);
  // var screen = BuyerViewStoreScreen(storeReference: reference,);
  // var route = MaterialPageRoute(builder: (context) => screen);
  Navigator.push(AppConstants.currentSelectedTabContext, route);
  return;
  //
  //
  //   ///If user is on Buyer view
  // if(AppConstants.appData.storeReference==null){
  //   AppConstants.userLevelPersistentTabController.jumpToTab(0);
  //   //Refresh button navigation
  //   AppConstants.bottomNavigationRefreshCtrl.sink.add(true);
  //   // Navigate to Comment screen
  //   var screen = BuyerProductCommentScreen(
  //     storeReference: storeReference,
  //     productRef: productReference,
  //     isWriteComment: false,
  //     selectedCommentId:commentId,
  //   );
  //   var route = MaterialPageRoute(builder: (context) => screen);
  //   // var screen = BuyerViewStoreScreen(storeReference: reference,);
  //   // var route = MaterialPageRoute(builder: (context) => screen);
  //   Navigator.push(AppConstants.currentSelectedTab, route);
  //   return;
  // }
  // ///If user is on Seller view
  // else{
  //   AppConstants.storeLevelPersistentTabController.jumpToTab(0);
  //   //Refresh button navigation
  //   AppConstants.bottomNavigationRefreshCtrl.sink.add(true);
  //   // Navigate to Comment screen
  //   var screen = BuyerProductCommentScreen(
  //     storeReference: storeReference,
  //     productRef: productReference,
  //     isWriteComment: false,
  //     selectedCommentId:commentId,
  //
  //   );
  //   var route = MaterialPageRoute(builder: (context) => screen);
  //   // var screen = BuyerViewStoreScreen(storeReference: reference,);
  //   // var route = MaterialPageRoute(builder: (context) => screen);
  //   Navigator.push(AppConstants.dashBoardContext, route);
  //   // // Navigate to Product screen
  //   // var screen = BuyerViewStoreScreen(storeReference: reference,);
  //   // var route = MaterialPageRoute(builder: (context) => screen);
  //   // Navigator.push(AppConstants.dashBoardContext, route);
  //
  //   return;
  //
  // }
}
//endregion



}