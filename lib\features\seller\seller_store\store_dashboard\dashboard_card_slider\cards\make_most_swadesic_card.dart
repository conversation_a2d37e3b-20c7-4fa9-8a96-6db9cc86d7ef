import 'package:flutter/material.dart';
import 'package:swadesic/util/app_colors.dart';
import 'package:swadesic/util/app_text_style.dart';
import 'package:swadesic/util/common_methods.dart';

class MakeMostSwadesicCard {
  static Widget buildTitle() {
    return Text(
      "Make the Most of Swadesic",
      style: AppTextStyle.contentHeading0(textColor: AppColors.appBlack),
    );
  }

  static Widget buildContent() {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Expanded(
          child: Text(
            "See all the ways Swadesic can work for you — explore every feature and benefit for your store.",
            style: AppTextStyle.smallTextRegular(textColor: AppColors.appBlack),
            maxLines: 3,
            overflow: TextOverflow.ellipsis,
          ),
        ),
        Container(
          padding: const EdgeInsets.all(5),
          child: Icon(Icons.arrow_forward, color: AppColors.appBlack),
        ),
      ],
    );
  }

  static void showBottomSheet(BuildContext context) {
    CommonMethods.appMinimumBottomSheets(
      bottomSheetName: "Make Most Out of Swadesic",
      screen: SingleChildScrollView(
        child: Padding(
          padding: const EdgeInsets.all(20),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const SizedBox(height: 12),
              _buildNumberedItem(1,
                  "Complete store profile (logo, cover, bio, accurate location)."),
              _buildNumberedItem(2,
                  "List at least 10–15 SKUs with high-quality images & benefit-driven descriptions."),
              _buildNumberedItem(
                  3, "Tag products correctly and add variants where possible."),
              _buildNumberedItem(
                  4, "Invite your existing network to follow your store."),
              _buildNumberedItem(5,
                  "Share your store link on all social media, WhatsApp, and offline signage."),
              _buildNumberedItem(6,
                  "Grow community by adding more supporters inside Swadesic."),
              _buildNumberedItem(
                  7, "Use product codes for easy sharing with buyers."),
              _buildNumberedItem(8,
                  "Tag products in posts and post regular updates in store feed."),
              _buildNumberedItem(9,
                  "Request reviews from past buyers & gather reviews for Swadesic orders."),
              _buildNumberedItem(10,
                  "Respond to orders instantly and keep store active with fresh listings."),
            ],
          ),
        ),
      ),
      context: context,
    );
  }

  // Helper widget for numbered list items within bottom sheet
  static Widget _buildNumberedItem(int number, String text) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            "$number.",
            style: AppTextStyle.contentText0(textColor: AppColors.appBlack),
          ),
          const SizedBox(width: 8),
          Expanded(
            child: Text(
              text,
              style: AppTextStyle.contentText0(textColor: AppColors.appBlack),
            ),
          ),
        ],
      ),
    );
  }
}
