import 'package:flutter/material.dart';
import 'package:swadesic/util/auto_hide_navigation_service.dart';

/// Wrapper widget that automatically enables scroll-to-hide navigation
/// for any page that contains scrollable content
class NavigationPageWrapper extends StatefulWidget {
  final Widget child;
  final ScrollController? scrollController;
  final bool enableAutoHide;

  const NavigationPageWrapper({
    super.key,
    required this.child,
    this.scrollController,
    this.enableAutoHide = true,
  });

  @override
  State<NavigationPageWrapper> createState() => _NavigationPageWrapperState();
}

class _NavigationPageWrapperState extends State<NavigationPageWrapper> {
  final AutoHideNavigationService _autoHideService = AutoHideNavigationService();
  ScrollController? _internalScrollController;

  @override
  void initState() {
    super.initState();
    
    if (widget.enableAutoHide) {
      _autoHideService.enableAutoHide();
      
      // Use provided scroll controller or create internal one
      if (widget.scrollController != null) {
        _autoHideService.attachToScrollController(widget.scrollController!);
      } else {
        // Try to find scroll controllers in the widget tree
        WidgetsBinding.instance.addPostFrameCallback((_) {
          _findAndAttachScrollControllers();
        });
      }
    }
  }

  @override
  void dispose() {
    if (widget.enableAutoHide) {
      if (widget.scrollController != null) {
        _autoHideService.detachFromScrollController(widget.scrollController!);
      }
      if (_internalScrollController != null) {
        _autoHideService.detachFromScrollController(_internalScrollController!);
      }
      _autoHideService.disableAutoHide();
    }
    super.dispose();
  }

  /// Find and attach to scroll controllers in the widget tree
  void _findAndAttachScrollControllers() {
    // This is a simplified approach - in a real implementation,
    // you might want to use a more sophisticated method to find scroll controllers
    // For now, we'll rely on the pages to provide their scroll controllers
  }

  @override
  Widget build(BuildContext context) {
    return widget.child;
  }
}

/// Mixin to easily integrate auto-hide functionality into navigation pages
mixin NavigationPageMixin<T extends StatefulWidget> on State<T> {
  final AutoHideNavigationService _autoHideService = AutoHideNavigationService();

  /// Enable auto-hide for this page
  void enableNavigationAutoHide() {
    _autoHideService.enableAutoHide();
  }

  /// Disable auto-hide for this page
  void disableNavigationAutoHide() {
    _autoHideService.disableAutoHide();
  }

  /// Attach scroll controller to auto-hide service
  void attachScrollControllerToNavigation(ScrollController scrollController) {
    _autoHideService.attachToScrollController(scrollController);
  }

  /// Detach scroll controller from auto-hide service
  void detachScrollControllerFromNavigation(ScrollController scrollController) {
    _autoHideService.detachFromScrollController(scrollController);
  }

  /// Force show navigation bars
  void forceShowNavigation() {
    _autoHideService.forceShowNavigationBars();
  }

  /// Force hide navigation bars
  void forceHideNavigation() {
    _autoHideService.forceHideNavigationBars();
  }

  @override
  void dispose() {
    _autoHideService.disableAutoHide();
    super.dispose();
  }
}
